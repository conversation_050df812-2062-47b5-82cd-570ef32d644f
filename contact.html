<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact Us - FitnessPro AI</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💪</text></svg>">

  <!-- EmailJS SDK -->
  <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
</head>
<body>
  <header>
    <div class="logo">
      <h1>FitnessPro AI</h1>
    </div>
    <nav>
      <ul>
        <li><a href="index.html">BMI Calculator</a></li>
        <li><a href="index.html#workout">Workout Plans</a></li>
        <li><a href="index.html#nutrition">Nutrition</a></li>
        <li><a href="index.html#about">About</a></li>
        <li><a href="contact.html" class="active">Contact</a></li>
      </ul>
    </nav>
    <div class="theme-toggle">
      <button id="themeToggle">
        <i class="fas fa-moon"></i>
      </button>
    </div>
  </header>

  <main class="container">
    <section class="contact-section">
      <div class="contact-header">
        <h2><i class="fas fa-envelope"></i> Contact Us</h2>
        <p>Have questions or feedback? We'd love to hear from you!</p>
      </div>

      <div class="contact-content">
        <div class="contact-info">
          <div class="info-card">
            <div class="info-icon">
              <i class="fas fa-envelope"></i>
            </div>
            <h3>Email Us</h3>
            <p><EMAIL></p>
          </div>

          <div class="info-card">
            <div class="info-icon">
              <i class="fas fa-clock"></i>
            </div>
            <h3>Response Time</h3>
            <p>We typically respond within 24 hours</p>
          </div>

          <div class="info-card">
            <div class="info-icon">
              <i class="fas fa-heart"></i>
            </div>
            <h3>Support</h3>
            <p>Dedicated to helping you achieve your fitness goals</p>
          </div>

          <div class="info-card alternative-contact">
            <div class="info-icon">
              <i class="fas fa-exclamation-circle"></i>
            </div>
            <h3>Can't Connect?</h3>
            <p>If you're having trouble with the contact form, you can reach us directly at:</p>
            <a href="mailto:<EMAIL>" class="direct-email-link">
              <i class="fas fa-envelope"></i>
              <EMAIL>
            </a>
          </div>
        </div>

        <div class="contact-form-container">
          <!-- Demo Notice - Remove this when EmailJS is configured -->
          <div class="demo-notice" id="demoNotice">
            <i class="fas fa-info-circle"></i>
            <strong>Demo Mode:</strong> This form is currently in demo mode. Follow the setup guide to enable real email sending.
          </div>

          <form id="contactForm" class="contact-form">
            <div class="form-row">
              <div class="form-group">
                <label for="name"><i class="fas fa-user"></i> Full Name</label>
                <input type="text" id="name" name="from_name" required>
                <span class="form-error" id="nameError"></span>
              </div>

              <div class="form-group">
                <label for="email"><i class="fas fa-envelope"></i> Email Address</label>
                <input type="email" id="email" name="from_email" required>
                <span class="form-error" id="emailError"></span>
              </div>
            </div>

            <div class="form-group">
              <label for="subject"><i class="fas fa-tag"></i> Subject</label>
              <select id="subject" name="subject" required>
                <option value="">Select a subject...</option>
                <option value="General Inquiry">General Inquiry</option>
                <option value="Technical Support">Technical Support</option>
                <option value="Feature Request">Feature Request</option>
                <option value="Bug Report">Bug Report</option>
                <option value="Partnership">Partnership Opportunity</option>
                <option value="Other">Other</option>
              </select>
              <span class="form-error" id="subjectError"></span>
            </div>

            <div class="form-group">
              <label for="message"><i class="fas fa-comment"></i> Message</label>
              <textarea id="message" name="message" rows="6" placeholder="Tell us how we can help you..." required></textarea>
              <span class="form-error" id="messageError"></span>
              <div class="character-count">
                <span id="charCount">0</span>/500 characters
              </div>
            </div>

            <button type="submit" class="btn-primary contact-submit-btn" id="submitBtn">
              <span class="btn-text">
                <i class="fas fa-paper-plane"></i> Send Message
              </span>
            </button>

            <div class="form-help-text">
              <i class="fas fa-info-circle"></i>
              <span>Having trouble? Email us directly at <a href="mailto:<EMAIL>"><EMAIL></a></span>
            </div>
          </form>

          <!-- Success/Error Messages -->
          <div id="formMessages" class="form-messages" style="display: none;">
            <div id="successMessage" class="message success-message" style="display: none;">
              <i class="fas fa-check-circle"></i>
              <h3>Message Sent Successfully!</h3>
              <p>Thank you for contacting us. We'll get back to you within 24 hours.</p>
            </div>

            <div id="errorMessage" class="message error-message" style="display: none;">
              <i class="fas fa-exclamation-triangle"></i>
              <h3>Oops! Something went wrong</h3>
              <p>We couldn't send your message through the contact form. Please try again, or reach out to us directly at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <footer>
    <p>&copy; 2025 FitnessPro AI. All rights reserved.</p>
  </footer>

  <script src="script.js"></script>
  <script src="contact.js"></script>
</body>
</html>

