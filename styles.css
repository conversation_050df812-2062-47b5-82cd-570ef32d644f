@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --accent-color: #4cc9f0;
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --gray-color: #6c757d;
  --border-radius: 10px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --hover-transform: translateY(-5px);
  --transition: all 0.3s ease;
  
  /* Background and text colors */
  --bg-color: #f5f7fa;
  --text-color: var(--dark-color);
  --card-bg: white;
  --header-bg: white;
  --footer-bg: var(--dark-color);
  --footer-text: white;
}

/* Dark mode colors */
[data-theme="dark"] {
  --primary-color: #6c8cff;
  --secondary-color: #5e43d8;
  --accent-color: #67d7ff;
  --bg-color: #121212;
  --text-color: #f8f9fa;
  --card-bg: #1e1e1e;
  --header-bg: #1e1e1e;
  --gray-color: #adb5bd;
  --light-color: #2c2c2c;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Poppins', sans-serif;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
}

/* Enhanced Header & Navigation */
header {
  background-color: var(--header-bg);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 1.2rem 5%;
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

/* Theme toggle button */
.theme-toggle {
  margin-left: 1rem;
}

.theme-toggle button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--light-color);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.theme-toggle button:hover {
  transform: rotate(15deg);
}

/* Logo styling */
.logo {
  display: flex;
  align-items: center;
}

.logo h1 {
  font-size: 1.6rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .logo h1 {
    font-size: 1.4rem;
  }
}

nav ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

nav a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 600;
  padding: 0.5rem 0;
  position: relative;
  transition: all 0.3s ease;
}

nav a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: all 0.3s ease;
}

nav a:hover::after, 
nav a.active::after {
  width: 100%;
}

/* Hero Section */
.hero {
  background: var(--gradient-primary);
  color: white;
  padding: 7rem 5%;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-1.2.1&auto=format&fit=crop&w=1500&q=80');
  background-size: cover;
  background-position: center;
  opacity: 0.1;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
}

.hero h2 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
}

/* Container & Cards */
.container {
  max-width: 1200px;
  margin: 3rem auto;
  padding: 0 20px;
}

/* Card backgrounds */
.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 2rem;
  transition: all 0.4s ease;
}

.card:hover {
  transform: var(--hover-transform);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 2rem;
  background: var(--gradient-primary);
  color: white;
  border-bottom: none;
}

.card-header h2 {
  color: white;
  margin-bottom: 0.5rem;
}

.card-header p {
  color: rgba(255, 255, 255, 0.8);
}

/* Input Section */
.input-section {
  padding: 1.5rem;
}

.unit-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  gap: 10px;
}

.unit-toggle button {
  padding: 0.5rem 1.5rem;
  background-color: var(--light-color);
  border: 1px solid #ddd;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.unit-toggle button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.metric-inputs, .imperial-inputs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;
}

label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--dark-color);
}

input, select {
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
}

input:focus, select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

/* Enhanced Input Fields */
.input-group input, 
.input-group select {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 12px 15px;
  transition: all 0.3s ease;
}

.input-group input:focus, 
.input-group select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.15);
}

/* Buttons */
.btn-primary, .btn-secondary {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  transition: var(--transition);
  text-decoration: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(67, 97, 238, 0.6);
}

.btn-secondary {
  background-color: var(--light-color);
  color: var(--dark-color);
  margin-right: 10px;
}

.btn-secondary:hover {
  background-color: #e9ecef;
}

button#calculate {
  width: 100%;
  padding: 1rem;
  font-size: 1.1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

/* Results Section */
.results {
  padding-bottom: 1.5rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  padding: 1.5rem;
}

.metric-box {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  transition: var(--transition);
}

.metric-box:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

.metric-box h3 {
  color: var(--gray-color);
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.metric-category, .metric-info {
  font-size: 0.9rem;
  color: var(--gray-color);
}

/* Recommendation Section */
.recommendation-section {
  padding: 1.5rem;
  border-top: 1px solid #eee;
}

.recommendation-section h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.recommendation-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-weight: 500;
  color: var(--gray-color);
  transition: var(--transition);
  border-radius: 5px;
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.tab-content {
  display: none;
  padding: 1rem 0;
}

.tab-content.active {
  display: block;
}

/* Macros Chart */
.macros-container {
  margin-top: 1.5rem;
}

.macros-container h4 {
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.macros-chart {
  display: flex;
  height: 30px;
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.macro {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  transition: var(--transition);
}

.macro-percent {
  font-weight: 700;
  font-size: 0.9rem;
}

.macro-label {
  font-size: 0.8rem;
  position: absolute;
  bottom: -25px;
  color: var(--dark-color);
}

.protein {
  background-color: #4361ee;
}

.carbs {
  background-color: #4cc9f0;
}

.fat {
  background-color: #f72585;
}

/* Workout Plan */
.workout-plan {
  margin-top: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* Footer */
footer {
  background-color: var(--footer-bg);
  color: var(--footer-text);
  padding: 4rem 5%;
  background: var(--dark-color);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  color: white;
  font-size: 1.2rem;
  transition: var(--transition);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin-left: 10px;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--primary-color);
  transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
  header {
    flex-direction: column;
    padding: 1rem;
  }
  
  nav ul {
    margin-top: 1rem;
    gap: 1rem;
  }
  
  .hero {
    padding: 3rem 1rem;
  }
  
  .hero h2 {
    font-size: 2rem;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}

/* Responsive Design for theme toggle */
@media (max-width: 768px) {
  header {
    flex-wrap: wrap;
  }
  
  .theme-toggle {
    order: 1;
    margin: 1rem 0 0;
  }
}

/* Workout Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 0.5rem;
}

.workout-filters {
  margin-bottom: 2rem;
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  background-color: var(--light-color);
  border: 1px solid #ddd;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.workout-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.workout-card {
  background-color: white;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  will-change: transform, opacity, box-shadow;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

.workout-card:hover {
  transform: var(--hover-transform);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.18);
}

/* Enhanced animation states for workout cards */
.workout-card.animate {
  transform: translateY(0) scale(1);
  opacity: 1;
  filter: blur(0);
}

/* Loading shimmer effect for cards before animation */
.workout-card:not(.animate)::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.workout-image {
  height: 220px;
  overflow: hidden;
}

.workout-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.workout-card:hover .workout-image img {
  transform: scale(1.05);
}

.workout-header {
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.workout-header h3 {
  margin: 0;
  color: var(--dark-color);
}

.workout-duration {
  font-size: 0.9rem;
  color: var(--gray-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.workout-body {
  padding: 1rem;
}

.workout-body p {
  margin-bottom: 1rem;
  color: var(--gray-color);
}

.workout-exercises {
  list-style-position: inside;
  margin-bottom: 1rem;
  padding-left: 0;
}

.workout-exercises li {
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.workout-benefits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.benefit {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  font-size: 0.85rem;
  background-color: #f8f9fa;
  padding: 0.3rem 0.6rem;
  border-radius: 20px;
  color: var(--gray-color);
}

.benefit i {
  color: var(--success-color);
}

.workout-footer {
  padding: 1rem;
  border-top: 1px solid #eee;
  text-align: center;
}

/* Media Queries for Workout Section */
@media (max-width: 992px) {
  .workout-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .workout-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin: 0 auto;
  }
  
  .workout-card {
    max-width: 100%;
  }
  
  .filter-options {
    justify-content: center;
  }
  
  .filter-btn {
    padding: 0.5rem 0.8rem;
    font-size: 0.9rem;
  }
  
  .filter-group label {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .workout-image {
    height: 180px;
  }
  
  .workout-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .workout-duration {
    margin-left: 0;
  }
  
  .workout-exercises li {
    font-size: 0.9rem;
  }
  
  .benefit {
    font-size: 0.8rem;
  }
}

/* Workout Modal Styles */
.workout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.workout-modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: white;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  padding: 2rem;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--dark-color);
  transition: var(--transition);
}

.modal-close:hover {
  color: var(--primary-color);
}

.workout-video {
  margin-bottom: 2rem;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.workout-details h3 {
  color: var(--primary-color);
  margin-top: 2rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.exercise-detail {
  margin-bottom: 2rem;
}

.exercise-detail h4 {
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.exercise-detail img {
  width: 100%;
  max-width: 500px;
  border-radius: var(--border-radius);
  margin-top: 1rem;
}

@media (max-width: 768px) {
  .modal-content {
    padding: 1.5rem;
    width: 95%;
  }
  
  .workout-video iframe {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    padding: 1rem;
  }
  
  .workout-video iframe {
    height: 200px;
  }
  
  .exercise-detail {
    margin-bottom: 1.5rem;
  }
}

/* Nutrition Section Styles */
.nutrition-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
}

.nutrition-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition);
  position: relative;
  padding-top: 60px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: all 0.4s ease;
}

.nutrition-card:hover {
  transform: var(--hover-transform);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.nutrition-image {
  height: 180px;
  overflow: hidden;
}

.nutrition-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.nutrition-card:hover .nutrition-image img {
  transform: scale(1.05);
}

.nutrition-icon {
  position: absolute;
  top: 160px;
  left: 20px;
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nutrition-card h3 {
  margin: 1.5rem 0 0.5rem 0;
  padding: 0 1.5rem;
  color: var(--dark-color);
}

.nutrition-card p {
  padding: 0 1.5rem;
  color: var(--gray-color);
  margin-bottom: 1rem;
}

.nutrition-card ul {
  padding: 0 1.5rem 1.5rem;
  list-style-position: inside;
}

.nutrition-card ul li {
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.food-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0 1.5rem 1.5rem;
}

.food-item {
  background-color: var(--light-color);
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.85rem;
  color: var(--gray-color);
}

/* Media Queries for Nutrition Section */
@media (max-width: 768px) {
  .nutrition-grid {
    grid-template-columns: 1fr;
  }
  
  .nutrition-card {
    max-width: 400px;
    margin: 0 auto;
  }
}

/* About Section Styles */
.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.about-image {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.about-image img {
  width: 100%;
  height: auto;
  display: block;
}

.about-content h3 {
  color: var(--primary-color);
  margin: 1.5rem 0 1rem;
}

.about-content h3:first-child {
  margin-top: 0;
}

.about-content p {
  margin-bottom: 1rem;
  color: var(--gray-color);
}

.about-content ul {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.about-content li {
  margin-bottom: 0.5rem;
}

.cta-buttons {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
}

/* Media Queries for About Section */
@media (max-width: 768px) {
  .about-grid {
    grid-template-columns: 1fr;
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .cta-buttons .btn-primary,
  .cta-buttons .btn-secondary {
    width: 100%;
    text-align: center;
  }
}

/* Workout Goals Section Styles */
#workout-goals {
  background-color: #f8f9fa;
  padding: 3rem 0;
  margin-top: 2rem;
}

.goals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.goal-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2rem;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: all 0.4s ease;
}

.goal-card:hover {
  transform: var(--hover-transform);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.goal-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.goal-icon i {
  font-size: 2rem;
  color: white;
}

.goal-card h3 {
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.goal-card p {
  color: var(--gray-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.goal-benefits {
  list-style: none;
  margin-bottom: 1.5rem;
  padding-left: 0;
}

.goal-benefits li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
}

.goal-benefits li:before {
  content: "\f00c";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  color: var(--success-color);
  position: absolute;
  left: 0;
}

.goal-cta {
  margin-top: 1.5rem;
}

.goal-card:after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, transparent 50%, rgba(67, 97, 238, 0.1) 50%);
  border-radius: 0 0 0 100px;
  z-index: 0;
}

/* Media Queries for Goals Section */
@media (max-width: 768px) {
  .goals-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
    margin: 2rem auto 0;
  }
  
  .goal-card {
    padding: 1.5rem;
  }
  
  .goal-icon {
    width: 60px;
    height: 60px;
  }
  
  .goal-icon i {
    font-size: 1.5rem;
  }
}

/* How It Works Section - Updated Design */
.how-it-works-grid {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  margin: 3rem 0;
  flex-wrap: wrap;
  text-align: center;
}

.section-header {
  text-align: center;
  margin-bottom: 2rem;
}

.section-header h2 {
  font-size: 2.2rem;
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.step-card {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  transition: var(--transition);
  background-color: transparent;
  box-shadow: none;
}

.step-number {
  width: 60px;
  height: 60px;
  background-color: #4361ee;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  font-weight: bold;
}

.step-card h3 {
  margin-bottom: 0.8rem;
  color: var(--dark-color);
  font-weight: 600;
}

.step-card p {
  color: var(--gray-color);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Add a subtle divider between steps */
.how-it-works-grid::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 15%;
  width: 70%;
  height: 1px;
  background-color: #e9ecef;
  z-index: -1;
}

@media (max-width: 768px) {
  .how-it-works-grid {
    flex-direction: column;
    gap: 3rem;
  }
  
  .how-it-works-grid::after {
    display: none;
  }
}

/* Animations */
/* Enhanced Animation System */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px) translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px) translateY(20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateY(0) scale(1);
  }
}

/* Base state for elements before animation */
.workout-card:not(.animate) {
  opacity: 0;
  transform: translateY(40px) scale(0.95);
  filter: blur(2px);
}

.card:not(.animate) {
  opacity: 0;
  transform: translateY(30px) scale(0.98);
}

.step-card:not(.animate) {
  opacity: 0;
  transform: translateY(25px) scale(0.97);
}

.nutrition-card:not(.animate) {
  opacity: 0;
  transform: translateY(35px) scale(0.96);
}

/* Animation classes */
.workout-card.animate {
  animation: fadeInUp 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.card.animate {
  animation: fadeInUp 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.step-card.animate {
  animation: fadeInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.nutrition-card.animate {
  animation: fadeInRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Staggered animations with improved timing */
.workout-card:nth-child(1).animate { animation-delay: 0.1s; }
.workout-card:nth-child(2).animate { animation-delay: 0.2s; }
.workout-card:nth-child(3).animate { animation-delay: 0.3s; }
.workout-card:nth-child(4).animate { animation-delay: 0.4s; }
.workout-card:nth-child(5).animate { animation-delay: 0.5s; }
.workout-card:nth-child(6).animate { animation-delay: 0.6s; }

.step-card:nth-child(1).animate { animation-delay: 0.1s; }
.step-card:nth-child(2).animate { animation-delay: 0.25s; }
.step-card:nth-child(3).animate { animation-delay: 0.4s; }
.step-card:nth-child(4).animate { animation-delay: 0.55s; }

.nutrition-card:nth-child(1).animate { animation-delay: 0.1s; }
.nutrition-card:nth-child(2).animate { animation-delay: 0.2s; }
.nutrition-card:nth-child(3).animate { animation-delay: 0.3s; }
.nutrition-card:nth-child(4).animate { animation-delay: 0.4s; }

/* Accessibility: Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .workout-card:not(.animate),
  .card:not(.animate),
  .step-card:not(.animate),
  .nutrition-card:not(.animate) {
    opacity: 0.7;
    transform: none;
    filter: none;
  }

  .workout-card.animate,
  .card.animate,
  .step-card.animate,
  .nutrition-card.animate {
    animation: none;
    opacity: 1;
    transform: none;
    filter: none;
    transition: opacity 0.3s ease;
  }

  .workout-card:not(.animate)::before {
    display: none;
  }
}

/* Performance optimizations for mobile devices */
@media (max-width: 768px) {
  .workout-card {
    will-change: auto; /* Reduce GPU usage on mobile */
  }

  .workout-card:not(.animate) {
    transform: translateY(20px) scale(0.98); /* Reduced transform for mobile */
    filter: none; /* Remove blur on mobile for better performance */
  }

  .workout-card.animate {
    animation-duration: 0.6s; /* Faster animations on mobile */
  }

  /* Reduce shimmer effect intensity on mobile */
  .workout-card:not(.animate)::before {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation-duration: 2s; /* Slower shimmer on mobile */
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .workout-card:not(.animate)::before {
    display: none; /* Remove shimmer in high contrast mode */
  }
}

/* Contact form styles */
.contact-section {
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--card-bg);
  border-radius: 10px;
  box-shadow: var(--box-shadow);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  background: var(--input-bg);
  color: var(--text-color);
  font-family: inherit;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 5px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s, opacity 0.3s;
}

.btn:hover {
  transform: translateY(-2px);
  opacity: 0.9;
}

.btn:active {
  transform: translateY(1px);
}

