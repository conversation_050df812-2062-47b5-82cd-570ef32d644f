<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FitnessPro AI</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  
  <!-- Favicon using data URI -->
  <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💪</text></svg>">
</head>
<body>
  <header>
    <div class="logo">
      <h1>FitnessPro AI</h1>
    </div>
    <nav>
      <ul>
        <li><a href="#calculator" class="active">BMI Calculator</a></li>
        <li><a href="#workout">Workout Plans</a></li>
        <li><a href="#nutrition">Nutrition</a></li>
        <li><a href="#about">About</a></li>
        <li><a href="contact.html">Contact</a></li>
      </ul>
    </nav>
    <div class="theme-toggle">
      <button id="themeToggle">
        <i class="fas fa-moon"></i>
      </button>
    </div>
  </header>

  <div class="hero">
    <div class="hero-content">
      <h2>Your Personal Fitness Assistant</h2>
      <p>Get personalized health insights and recommendations based on your body metrics</p>
      <a href="#calculator" class="btn-primary">Get Started</a>
    </div>
  </div>

  <!-- Calculator Section -->
  <div class="container" id="calculator">
    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-calculator"></i> Body Metrics Calculator</h2>
        <p>Calculate your BMI and get personalized recommendations</p>
      </div>
      
      <div class="input-section">
        <div class="unit-toggle">
          <button id="metricBtn" class="active">Metric</button>
          <button id="imperialBtn">Imperial</button>
        </div>
        
        <div class="metric-inputs">
          <div class="input-group">
            <label for="age">Age:</label>
            <input type="number" id="age" min="15" max="100" required>
          </div>
          <div class="input-group">
            <label for="gender">Gender:</label>
            <select id="gender">
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
          <div class="input-group">
            <label for="weight">Weight (kg):</label>
            <input type="number" id="weight" min="20" max="300" required>
          </div>
          <div class="input-group">
            <label for="height">Height (cm):</label>
            <input type="number" id="height" min="50" max="250" required>
          </div>
          <div class="input-group">
            <label for="activity">Activity Level:</label>
            <select id="activity">
              <option value="sedentary">Sedentary (little or no exercise)</option>
              <option value="light">Light (1-3 days/week)</option>
              <option value="moderate">Moderate (3-5 days/week)</option>
              <option value="active">Active (6-7 days/week)</option>
              <option value="very">Very Active (2x per day)</option>
            </select>
          </div>
        </div>
        
        <div class="imperial-inputs" style="display: none;">
          <div class="input-group">
            <label for="ageImperial">Age:</label>
            <input type="number" id="ageImperial" min="15" max="100" required>
          </div>
          <div class="input-group">
            <label for="genderImperial">Gender:</label>
            <select id="genderImperial">
              <option value="male">Male</option>
              <option value="female">Female</option>
              <option value="other">Other</option>
            </select>
          </div>
          <div class="input-group">
            <label for="weightLbs">Weight (lbs):</label>
            <input type="number" id="weightLbs" min="40" max="660" required>
          </div>
          <div class="input-group">
            <label for="heightFt">Height (ft):</label>
            <input type="number" id="heightFt" min="1" max="8" required>
          </div>
          <div class="input-group">
            <label for="heightIn">Height (in):</label>
            <input type="number" id="heightIn" min="0" max="11" required>
          </div>
          <div class="input-group">
            <label for="activityImperial">Activity Level:</label>
            <select id="activityImperial">
              <option value="sedentary">Sedentary (little or no exercise)</option>
              <option value="light">Light (1-3 days/week)</option>
              <option value="moderate">Moderate (3-5 days/week)</option>
              <option value="active">Active (6-7 days/week)</option>
              <option value="very">Very Active (2x per day)</option>
            </select>
          </div>
        </div>
        
        <button id="calculate" class="btn-primary">Calculate <i class="fas fa-arrow-right"></i></button>
      </div>
    </div>
    
    <div class="results card" id="results" style="display: none;">
      <div class="card-header">
        <h2><i class="fas fa-chart-bar"></i> Your Results</h2>
      </div>
      
      <div class="metrics-grid">
        <div class="metric-box">
          <h3>BMI</h3>
          <div class="metric-value" id="bmiValue">0.0</div>
          <div class="metric-category" id="bmiCategory">-</div>
        </div>
        
        <div class="metric-box">
          <h3>BMR</h3>
          <div class="metric-value" id="bmrValue">0</div>
          <div class="metric-info">Calories/day at rest</div>
        </div>
        
        <div class="metric-box">
          <h3>Daily Calories</h3>
          <div class="metric-value" id="tdeeValue">0</div>
          <div class="metric-info">To maintain weight</div>
        </div>
        
        <div class="metric-box">
          <h3>Ideal Weight</h3>
          <div class="metric-value" id="idealWeight">0</div>
          <div class="metric-info">Based on height</div>
        </div>
      </div>
      
      <div class="recommendation-section">
        <h3><i class="fas fa-lightbulb"></i> Personalized Recommendations</h3>
        <div class="recommendation-tabs">
          <button class="tab-btn active" data-tab="general">General</button>
          <button class="tab-btn" data-tab="nutrition">Nutrition</button>
          <button class="tab-btn" data-tab="exercise">Exercise</button>
        </div>
        
        <div class="tab-content active" id="general-tab">
          <p id="generalRecommendation"></p>
        </div>
        
        <div class="tab-content" id="nutrition-tab">
          <p id="nutritionRecommendation"></p>
          <div class="macros-container">
            <h4>Recommended Macros:</h4>
            <div class="macros-chart">
              <div class="macro protein">
                <span class="macro-percent" id="proteinPercent">0%</span>
                <span class="macro-label">Protein</span>
              </div>
              <div class="macro carbs">
                <span class="macro-percent" id="carbsPercent">0%</span>
                <span class="macro-label">Carbs</span>
              </div>
              <div class="macro fat">
                <span class="macro-percent" id="fatPercent">0%</span>
                <span class="macro-label">Fat</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="tab-content" id="exercise-tab">
          <p id="exerciseRecommendation"></p>
          <div class="workout-plan" id="workoutPlan"></div>
        </div>
      </div>
      
      <button id="saveResults" class="btn-secondary"><i class="fas fa-save"></i> Save Results</button>
      <button id="shareResults" class="btn-secondary"><i class="fas fa-share-alt"></i> Share</button>
    </div>
  </div>
  
  <!-- Workout Plans Section -->
  <div class="container" id="workout">
    <div class="section-header">
      <h2><i class="fas fa-dumbbell"></i> Workout Plans</h2>
      <p>Personalized workout routines for your fitness goals</p>
    </div>
    
    <div class="workout-filters">
      <div class="filter-group">
        <label>Fitness Level:</label>
        <div class="filter-options">
          <button class="filter-btn active" data-filter="beginner">Beginner</button>
          <button class="filter-btn" data-filter="intermediate">Intermediate</button>
          <button class="filter-btn" data-filter="advanced">Advanced</button>
        </div>
      </div>
      
      <div class="filter-group">
        <label>Goal:</label>
        <div class="filter-options">
          <button class="filter-btn active" data-filter="strength">Strength</button>
          <button class="filter-btn" data-filter="cardio">Cardio</button>
          <button class="filter-btn" data-filter="flexibility">Flexibility</button>
          <button class="filter-btn" data-filter="weight-loss">Weight Loss</button>
        </div>
      </div>
    </div>
    
    <div class="workout-grid">
      <!-- Beginner Strength Workout -->
      <div class="workout-card" data-level="beginner" data-goal="strength">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Beginner Strength Training">
        </div>
        <div class="workout-header">
          <h3>Beginner Strength Training</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 30 min</span>
        </div>
        <div class="workout-body">
          <p>Perfect for beginners looking to build strength and muscle tone with minimal equipment.</p>
          <ul class="workout-exercises">
            <li>Bodyweight Squats: 3 sets x 12 reps</li>
            <li>Push-ups (or knee push-ups): 3 sets x 10 reps</li>
            <li>Dumbbell Rows: 3 sets x 10 reps each side</li>
            <li>Glute Bridges: 3 sets x 15 reps</li>
            <li>Plank: 3 sets x 30 seconds</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Builds foundational strength</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves posture</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Boosts metabolism</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
      
      <!-- Beginner Cardio Workout -->
      <div class="workout-card" data-level="beginner" data-goal="cardio">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Beginner Cardio">
        </div>
        <div class="workout-header">
          <h3>Beginner Cardio</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 20 min</span>
        </div>
        <div class="workout-body">
          <p>A simple cardio routine to improve heart health and endurance for beginners.</p>
          <ul class="workout-exercises">
            <li>Warm-up: 5 minutes brisk walking</li>
            <li>Alternating: 1 minute jogging, 2 minutes walking (repeat 5 times)</li>
            <li>Cool down: 5 minutes walking</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves cardiovascular health</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Builds endurance</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Burns calories</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
      
      <!-- Intermediate Strength Workout -->
      <div class="workout-card" data-level="intermediate" data-goal="strength" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Intermediate Strength Training">
        </div>
        <div class="workout-header">
          <h3>Intermediate Full Body Strength</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 45 min</span>
        </div>
        <div class="workout-body">
          <p>A comprehensive strength routine for those with some training experience.</p>
          <ul class="workout-exercises">
            <li>Barbell Squats: 4 sets x 10 reps</li>
            <li>Dumbbell Bench Press: 4 sets x 10 reps</li>
            <li>Bent-Over Rows: 4 sets x 10 reps</li>
            <li>Overhead Press: 3 sets x 10 reps</li>
            <li>Romanian Deadlifts: 3 sets x 10 reps</li>
            <li>Hanging Leg Raises: 3 sets x 12 reps</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Builds muscle mass</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Increases strength</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves body composition</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
      
      <!-- Intermediate Cardio Workout -->
      <div class="workout-card" data-level="intermediate" data-goal="cardio" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1538805060514-97d9cc17730c?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Intermediate Cardio">
        </div>
        <div class="workout-header">
          <h3>Intermediate Interval Training</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 40 min</span>
        </div>
        <div class="workout-body">
          <p>A balanced cardio workout that combines steady-state and interval training for improved endurance.</p>
          <ul class="workout-exercises">
            <li>Warm-up: 5 minutes light jogging</li>
            <li>Steady pace: 10 minutes at moderate intensity</li>
            <li>Intervals: 10 x (1 minute sprint, 1 minute walk)</li>
            <li>Steady pace: 5 minutes at moderate intensity</li>
            <li>Cool down: 5 minutes walking</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves aerobic capacity</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Burns significant calories</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Enhances recovery ability</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>

      <!-- Intermediate Flexibility Workout -->
      <div class="workout-card" data-level="intermediate" data-goal="flexibility" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1575052814086-f385e2e2ad1b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Intermediate Flexibility">
        </div>
        <div class="workout-header">
          <h3>Intermediate Yoga Flow</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 45 min</span>
        </div>
        <div class="workout-body">
          <p>A dynamic yoga sequence that builds strength while improving flexibility and balance.</p>
          <ul class="workout-exercises">
            <li>Sun Salutations: 5 rounds</li>
            <li>Warrior Sequence: 3 minutes each side</li>
            <li>Balance Poses: Tree, Eagle, Dancer (30 seconds each)</li>
            <li>Deep Stretches: Pigeon, Lizard, Half Split</li>
            <li>Inversions: Supported Headstand or Forearm Stand</li>
            <li>Savasana: 5 minutes</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Increases range of motion</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves balance and stability</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Reduces stress and tension</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>

      <!-- Intermediate Weight Loss Workout -->
      <div class="workout-card" data-level="intermediate" data-goal="weight-loss" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1434682881908-b43d0467b798?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Intermediate Weight Loss">
        </div>
        <div class="workout-header">
          <h3>Intermediate Metabolic Circuit</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 35 min</span>
        </div>
        <div class="workout-body">
          <p>A challenging circuit workout designed to maximize calorie burn and boost metabolism.</p>
          <ul class="workout-exercises">
            <li>Warm-up: 5 minutes dynamic movements</li>
            <li>Circuit (3 rounds, 45 seconds work, 15 seconds rest):</li>
            <li>- Kettlebell Swings</li>
            <li>- Push-ups</li>
            <li>- Dumbbell Lunges</li>
            <li>- Renegade Rows</li>
            <li>- Plank Jacks</li>
            <li>- Mountain Climbers</li>
            <li>Cool down: 5 minutes stretching</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Accelerates fat loss</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Preserves lean muscle</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Increases post-workout calorie burn</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
      
      <!-- Advanced Weight Loss Workout -->
      <div class="workout-card" data-level="advanced" data-goal="weight-loss" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1517838277536-f5f99be501cd?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Advanced HIIT">
        </div>
        <div class="workout-header">
          <h3>Advanced HIIT for Weight Loss</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 35 min</span>
        </div>
        <div class="workout-body">
          <p>High-intensity interval training designed for maximum calorie burn.</p>
          <ul class="workout-exercises">
            <li>Warm-up: 5 minutes dynamic stretching</li>
            <li>Circuit (4 rounds, 40 seconds work, 20 seconds rest):</li>
            <li>- Burpees</li>
            <li>- Mountain Climbers</li>
            <li>- Jump Squats</li>
            <li>- Push-up to Renegade Row</li>
            <li>- Box Jumps</li>
            <li>Cool down: 5 minutes stretching</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Maximizes calorie burn</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves cardiovascular fitness</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Boosts metabolism for hours</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
      
      <!-- Beginner Flexibility Workout -->
      <div class="workout-card" data-level="beginner" data-goal="flexibility">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1552196563-55cd4e45efb3?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Beginner Flexibility">
        </div>
        <div class="workout-header">
          <h3>Beginner Yoga Flow</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 25 min</span>
        </div>
        <div class="workout-body">
          <p>A gentle yoga sequence to improve flexibility and reduce stress.</p>
          <ul class="workout-exercises">
            <li>Child's Pose: 1 minute</li>
            <li>Cat-Cow Stretch: 10 repetitions</li>
            <li>Downward Dog: 30 seconds</li>
            <li>Low Lunge: 30 seconds each side</li>
            <li>Seated Forward Fold: 1 minute</li>
            <li>Supine Twist: 30 seconds each side</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves flexibility</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Reduces stress</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Enhances recovery</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
      
      <!-- Beginner Weight Loss Workout -->
      <div class="workout-card" data-level="beginner" data-goal="weight-loss">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1517836357463-d25dfeac3438?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Beginner Weight Loss">
        </div>
        <div class="workout-header">
          <h3>Beginner Weight Loss Circuit</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 30 min</span>
        </div>
        <div class="workout-body">
          <p>A simple circuit training workout designed for beginners looking to lose weight.</p>
          <ul class="workout-exercises">
            <li>Jumping Jacks: 30 seconds</li>
            <li>Wall Sit: 30 seconds</li>
            <li>Modified Push-ups: 10 reps</li>
            <li>Crunches: 15 reps</li>
            <li>Chair Step-ups: 10 each leg</li>
            <li>Rest 1 minute, repeat 3 times</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Burns calories</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Builds basic fitness</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Easy to follow</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>

      <!-- Advanced Strength Workout -->
      <div class="workout-card" data-level="advanced" data-goal="strength" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1574680096145-d05b474e2155?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Advanced Strength Training">
        </div>
        <div class="workout-header">
          <h3>Advanced Power Building</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 60 min</span>
        </div>
        <div class="workout-body">
          <p>A challenging strength program designed to build both power and muscle mass.</p>
          <ul class="workout-exercises">
            <li>Back Squats: 5 sets x 5 reps</li>
            <li>Deadlifts: 5 sets x 5 reps</li>
            <li>Bench Press: 5 sets x 5 reps</li>
            <li>Weighted Pull-ups: 4 sets x 8 reps</li>
            <li>Military Press: 4 sets x 8 reps</li>
            <li>Weighted Dips: 3 sets x 10 reps</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Maximizes strength gains</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Builds dense muscle</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves power output</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>

      <!-- Advanced Cardio Workout -->
      <div class="workout-card" data-level="advanced" data-goal="cardio" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1571008887538-b36bb32f4571?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Advanced Cardio">
        </div>
        <div class="workout-header">
          <h3>Advanced Endurance Training</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 50 min</span>
        </div>
        <div class="workout-body">
          <p>A challenging cardio routine to push your endurance to the next level.</p>
          <ul class="workout-exercises">
            <li>Warm-up: 5 minutes easy jogging</li>
            <li>Interval Set 1: 6 x 400m sprints (1 min rest)</li>
            <li>Recovery: 3 minutes easy jog</li>
            <li>Interval Set 2: 4 x 800m at 80% effort (2 min rest)</li>
            <li>Recovery: 3 minutes easy jog</li>
            <li>Finisher: 1 mile at tempo pace</li>
            <li>Cool down: 5 minutes walking + stretching</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Increases VO2 max</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Improves lactate threshold</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Enhances recovery capacity</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>

      <!-- Advanced Flexibility Workout -->
      <div class="workout-card" data-level="advanced" data-goal="flexibility" style="display: none;">
        <div class="workout-image">
          <img src="https://images.unsplash.com/photo-1566501206188-5dd0cf160a0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Advanced Flexibility">
        </div>
        <div class="workout-header">
          <h3>Advanced Yoga & Mobility</h3>
          <span class="workout-duration"><i class="far fa-clock"></i> 55 min</span>
        </div>
        <div class="workout-body">
          <p>An advanced practice combining deep stretching, challenging yoga poses, and mobility work.</p>
          <ul class="workout-exercises">
            <li>Dynamic Warm-up: 10 minutes</li>
            <li>Advanced Sun Salutations: 5 rounds</li>
            <li>Advanced Poses: Handstand, Forearm Stand, Bird of Paradise</li>
            <li>Deep Hip Openers: 10 minutes</li>
            <li>Backbend Sequence: Wheel, Camel, King Pigeon</li>
            <li>Mobility Flow: 10 minutes</li>
            <li>Meditation: 5 minutes</li>
          </ul>
          <div class="workout-benefits">
            <span class="benefit"><i class="fas fa-check-circle"></i> Develops extreme flexibility</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Builds body control</span>
            <span class="benefit"><i class="fas fa-check-circle"></i> Enhances mind-body connection</span>
          </div>
        </div>
        <div class="workout-footer">
        </div>
      </div>
    </div>
  </div>
  
  <!-- Nutrition Section -->
  <div class="container" id="nutrition">
    <div class="section-header">
      <h2><i class="fas fa-apple-alt"></i> Nutrition Guide</h2>
      <p>Healthy eating tips and meal plans for optimal fitness</p>
    </div>
    
    <div class="nutrition-grid">
      <div class="nutrition-card">
        <div class="nutrition-image">
          <img src="https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Balanced Diet">
        </div>
        <div class="nutrition-icon">
          <i class="fas fa-balance-scale"></i>
        </div>
        <h3>Balanced Diet</h3>
        <p>A balanced diet includes proteins, carbohydrates, healthy fats, vitamins, and minerals in appropriate proportions.</p>
        <ul>
          <li>Aim for 3-5 servings of vegetables daily</li>
          <li>Include lean protein with each meal</li>
          <li>Choose whole grains over refined carbs</li>
          <li>Stay hydrated with 2-3 liters of water daily</li>
        </ul>
      </div>
      
      <div class="nutrition-card">
        <div class="nutrition-image">
          <img src="https://images.unsplash.com/photo-1532550907401-a500c9a57435?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Protein Sources">
        </div>
        <div class="nutrition-icon">
          <i class="fas fa-drumstick-bite"></i>
        </div>
        <h3>Protein Sources</h3>
        <p>Protein is essential for muscle repair and growth. Include a variety of sources in your diet.</p>
        <div class="food-list">
          <span class="food-item">Chicken</span>
          <span class="food-item">Fish</span>
          <span class="food-item">Eggs</span>
          <span class="food-item">Greek Yogurt</span>
          <span class="food-item">Tofu</span>
          <span class="food-item">Lentils</span>
          <span class="food-item">Quinoa</span>
          <span class="food-item">Cottage Cheese</span>
        </div>
      </div>
      
      <div class="nutrition-card">
        <div class="nutrition-image">
          <img src="https://images.unsplash.com/photo-1505253716362-afaea1d3d1af?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Healthy Carbs">
        </div>
        <div class="nutrition-icon">
          <i class="fas fa-seedling"></i>
        </div>
        <h3>Healthy Carbs</h3>
        <p>Carbohydrates provide energy for workouts. Focus on complex carbs with fiber.</p>
        <div class="food-list">
          <span class="food-item">Sweet Potatoes</span>
          <span class="food-item">Brown Rice</span>
          <span class="food-item">Oats</span>
          <span class="food-item">Quinoa</span>
          <span class="food-item">Whole Grain Bread</span>
          <span class="food-item">Beans</span>
          <span class="food-item">Fruits</span>
          <span class="food-item">Vegetables</span>
        </div>
      </div>
      
      <div class="nutrition-card">
        <div class="nutrition-image">
          <img src="https://images.unsplash.com/photo-1519623286359-e9f3cbef015b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Healthy Fats">
        </div>
        <div class="nutrition-icon">
          <i class="fas fa-oil-can"></i>
        </div>
        <h3>Healthy Fats</h3>
        <p>Healthy fats support hormone production and nutrient absorption.</p>
        <div class="food-list">
          <span class="food-item">Avocados</span>
          <span class="food-item">Olive Oil</span>
          <span class="food-item">Nuts</span>
          <span class="food-item">Seeds</span>
          <span class="food-item">Fatty Fish</span>
          <span class="food-item">Eggs</span>
          <span class="food-item">Dark Chocolate</span>
          <span class="food-item">Coconut Oil</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- How It Works Section -->
  <div class="container" id="about">
    <div class="section-header">
      <h2>How It Works</h2>
    </div>
    
    <div class="how-it-works-grid">
      <div class="step-card">
        <div class="step-number">1</div>
        <h3>Enter Your Details</h3>
        <p>Provide your current measurements and fitness goals</p>
      </div>
      
      <div class="step-card">
        <div class="step-number">2</div>
        <h3>Get Your Plan</h3>
        <p>Receive a customized workout plan generated by AI</p>
      </div>
      
      <div class="step-card">
        <div class="step-number">3</div>
        <h3>Track Progress</h3>
        <p>Monitor your improvements and stay motivated</p>
      </div>
      
      <div class="step-card">
        <div class="step-number">4</div>
        <h3>Achieve Goals</h3>
        <p>Reach your fitness goals with expert guidance</p>
      </div>
    </div>
  </div>  
















<!-- Modal Templates (hidden) -->
<div id="modal-templates" style="display: none;">
  <!-- Beginner Flexibility Modal Content -->
  <div id="beginner-flexibility-content">
    <h2>Beginner Yoga Flow - Full Workout</h2>
    <div class="workout-video">
      <iframe width="100%" height="400" src="https://www.youtube.com/embed/v7AYKMP6rOE" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    </div>
    <div class="workout-details">
      <h3>Workout Overview</h3>
      <p>This gentle yoga flow is perfect for beginners looking to improve flexibility, reduce stress, and enhance recovery. No prior yoga experience is needed.</p>
      
      <h3>Equipment Needed</h3>
      <ul>
        <li>Yoga mat</li>
        <li>Comfortable clothing</li>
        <li>Optional: yoga blocks or a folded blanket for support</li>
      </ul>
      
      <h3>Detailed Exercise Instructions</h3>
      <div class="exercise-detail">
        <h4>1. Child's Pose (1 minute)</h4>
        <p><strong>Instructions:</strong> Kneel on the floor, touch your big toes together, and sit on your heels. Separate your knees about hip-width apart. Exhale and lay your torso down between your thighs. Extend your arms forward, palms down. Relax and breathe deeply.</p>
        <img src="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Child's Pose">
      </div>
      
      <div class="exercise-detail">
        <h4>2. Cat-Cow Stretch (10 repetitions)</h4>
        <p><strong>Instructions:</strong> Start on your hands and knees in a tabletop position. For Cat, exhale and round your spine toward the ceiling. For Cow, inhale and arch your back, lifting your chest and tailbone toward the ceiling. Flow between these two positions.</p>
        <img src="https://images.unsplash.com/photo-1588286840104-8957b019727f?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Cat-Cow Stretch">
      </div>
      
      <!-- More exercises would follow -->
    </div>
  </div>
  
  <!-- Intermediate Strength Modal Content -->
  <div id="intermediate-strength-content">
    <h2>Intermediate Full Body Strength - Full Workout</h2>
    <div class="workout-video">
      <iframe width="100%" height="400" src="https://www.youtube.com/embed/ixkQaZXVQjs" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    </div>
    <div class="workout-details">
      <h3>Workout Overview</h3>
      <p>This intermediate strength workout targets all major muscle groups for balanced development. Ideal for those with some strength training experience looking to build muscle and increase overall strength.</p>
      
      <h3>Equipment Needed</h3>
      <ul>
        <li>Barbell with weight plates</li>
        <li>Dumbbells (medium to heavy)</li>
        <li>Bench</li>
        <li>Pull-up bar</li>
      </ul>
      
      <h3>Detailed Exercise Instructions</h3>
      <div class="exercise-detail">
        <h4>1. Barbell Squats (4 sets x 10 reps)</h4>
        <p><strong>Rest:</strong> 90 seconds between sets</p>
        <p><strong>Instructions:</strong> Place a barbell on your upper back, feet shoulder-width apart. Bend at the knees and hips to lower your body until thighs are parallel to the ground. Push through your heels to return to standing.</p>
        <img src="https://images.unsplash.com/photo-1534438327276-14e5300c3a48?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Barbell Squat">
      </div>
      
      <div class="exercise-detail">
        <h4>2. Dumbbell Bench Press (4 sets x 10 reps)</h4>
        <p><strong>Rest:</strong> 90 seconds between sets</p>
        <p><strong>Instructions:</strong> Lie on a bench holding dumbbells at chest level. Press the weights up until arms are extended, then lower them back to the starting position with control.</p>
        <img src="https://images.unsplash.com/photo-1571019114274-c5a5201d3b51?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Dumbbell Bench Press">
      </div>
      
      <!-- More exercises would follow -->
    </div>
  </div>
  
  <!-- Advanced Cardio Modal Content -->
  <div id="advanced-cardio-content">
    <h2>Advanced Endurance Training - Full Workout</h2>
    <div class="workout-video">
      <iframe width="100%" height="400" src="https://www.youtube.com/embed/ixkQaZXVQjs" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
    </div>
    <div class="workout-details">
      <h3>Workout Overview</h3>
      <p>This advanced cardio workout is designed to push your cardiovascular system to its limits, improving VO2 max, lactate threshold, and overall endurance capacity.</p>
      
      <h3>Equipment Needed</h3>
      <ul>
        <li>Running shoes</li>
        <li>Track or measured distance markers</li>
        <li>Stopwatch or fitness tracker</li>
        <li>Water bottle</li>
      </ul>
      
      <h3>Detailed Exercise Instructions</h3>
      <div class="exercise-detail">
        <h4>Warm-up: 5 minutes easy jogging</h4>
        <p>Start with 5 minutes of easy jogging to prepare your body for the intense workout ahead.</p>
      </div>
      
      <div class="exercise-detail">
        <h4>Interval Set 1: 6 x 400m sprints</h4>
        <p><strong>Rest:</strong> 1 minute between intervals</p>
        <p><strong>Instructions:</strong> Run 400m (one lap of a standard track) at 85-90% of your maximum effort. Rest for 1 minute between each sprint. Focus on maintaining good form throughout.</p>
        <img src="https://images.unsplash.com/photo-1571008887538-b36bb32f4571?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="Sprint Training">
      </div>
      
      <!-- More exercises would follow -->
    </div>
  </div>
</div>

<!-- Workout Goals Section -->
<div class="container" id="workout-goals">
  <div class="section-header">
    <h2><i class="fas fa-bullseye"></i> Workout Goals</h2>
    <p>Choose the right workout based on your fitness goals</p>
  </div>
  
  <div class="goals-grid">
    <!-- Cardio Goal -->
    <div class="goal-card">
      <div class="goal-icon">
        <i class="fas fa-heartbeat"></i>
      </div>
      <h3>Cardio & Endurance</h3>
      <p>Improve your cardiovascular health, boost stamina, and increase your energy levels with our cardio-focused workouts.</p>
      <ul class="goal-benefits">
        <li>Strengthens heart and lungs</li>
        <li>Burns calories efficiently</li>
        <li>Improves mood and sleep quality</li>
        <li>Reduces risk of chronic diseases</li>
      </ul>
      <div class="goal-cta">
        <a href="#workout" class="btn-primary" onclick="filterWorkoutsByGoal('cardio')">View Cardio Workouts</a>
      </div>
    </div>
    
    <!-- Flexibility Goal -->
    <div class="goal-card">
      <div class="goal-icon">
        <i class="fas fa-child"></i>
      </div>
      <h3>Flexibility & Mobility</h3>
      <p>Enhance your range of motion, prevent injuries, and improve overall movement quality with our flexibility programs.</p>
      <ul class="goal-benefits">
        <li>Increases joint range of motion</li>
        <li>Reduces muscle tension and soreness</li>
        <li>Improves posture and balance</li>
        <li>Enhances athletic performance</li>
      </ul>
      <div class="goal-cta">
        <a href="#workout" class="btn-primary" onclick="filterWorkoutsByGoal('flexibility')">View Flexibility Workouts</a>
      </div>
    </div>
    
    <!-- Weight Loss Goal -->
    <div class="goal-card">
      <div class="goal-icon">
        <i class="fas fa-weight"></i>
      </div>
      <h3>Weight Loss</h3>
      <p>Achieve sustainable weight loss with our balanced approach combining effective workouts and nutritional guidance.</p>
      <ul class="goal-benefits">
        <li>Burns fat while preserving muscle</li>
        <li>Boosts metabolism long-term</li>
        <li>Improves body composition</li>
        <li>Sustainable, healthy approach</li>
      </ul>
      <div class="goal-cta">
        <a href="#workout" class="btn-primary" onclick="filterWorkoutsByGoal('weight-loss')">View Weight Loss Workouts</a>
      </div>
    </div>
  </div>
</div>


<footer>
  <div class="footer-content">
    <div class="footer-logo">
      <i class="fas fa-heartbeat"></i>
      <h3>FitnessPro AI</h3>
    </div>
    <p>© 2025 FitnessPro AI. All rights reserved.</p>
    <div class="social-links">
      <a href="#"><i class="fab fa-facebook"></i></a>
      <a href="#"><i class="fab fa-twitter"></i></a>
      <a href="#"><i class="fab fa-instagram"></i></a>
      <a href="#"><i class="fab fa-youtube"></i></a>
    </div>
  </div>
</footer>

<script src="script.js"></script>
</body>
</html>



