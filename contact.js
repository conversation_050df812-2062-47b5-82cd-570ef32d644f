// Contact Form EmailJS Integration
// EmailJS Configuration - Update these with your actual EmailJS credentials
const EMAILJS_CONFIG = {
  serviceID: 'service_sug1zfk', // Your EmailJS service ID
  templateID: 'template_fomy3pc', // Your EmailJS template ID
  publicKey: 'oObHJzu95pnwwDS6t' // Your EmailJS public key from dashboard
};

// Demo mode - Set to false since you've configured EmailJS
const DEMO_MODE = false;

// Initialize EmailJS when the page loads
document.addEventListener('DOMContentLoaded', function() {
  // Hide demo notice if not in demo mode
  const demoNotice = document.getElementById('demoNotice');
  if (!DEMO_MODE && demoNotice) {
    demoNotice.style.display = 'none';
  }

  // Initialize EmailJS with your public key (only if not in demo mode)
  if (!DEMO_MODE) {
    emailjs.init(EMAILJS_CONFIG.publicKey);
  }
  
  // Get form elements
  const contactForm = document.getElementById('contactForm');
  const submitBtn = document.getElementById('submitBtn');
  const formMessages = document.getElementById('formMessages');
  const successMessage = document.getElementById('successMessage');
  const errorMessage = document.getElementById('errorMessage');
  const messageTextarea = document.getElementById('message');
  const charCount = document.getElementById('charCount');
  
  // Character counter for message textarea
  messageTextarea.addEventListener('input', function() {
    const currentLength = this.value.length;
    const maxLength = 500;
    
    charCount.textContent = currentLength;
    
    if (currentLength > maxLength) {
      charCount.style.color = '#ef4444';
      this.value = this.value.substring(0, maxLength);
      charCount.textContent = maxLength;
    } else if (currentLength > maxLength * 0.9) {
      charCount.style.color = '#f59e0b';
    } else {
      charCount.style.color = '#6b7280';
    }
  });
  
  // Form validation functions
  function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  function validateForm() {
    let isValid = true;
    const formData = new FormData(contactForm);
    
    // Clear previous errors
    document.querySelectorAll('.form-error').forEach(error => {
      error.classList.remove('show');
    });
    document.querySelectorAll('.form-group').forEach(group => {
      group.classList.remove('error', 'success');
    });
    
    // Validate name
    const name = formData.get('from_name').trim();
    if (name.length < 2) {
      showFieldError('name', 'Name must be at least 2 characters long');
      isValid = false;
    } else {
      showFieldSuccess('name');
    }
    
    // Validate email
    const email = formData.get('from_email').trim();
    if (!validateEmail(email)) {
      showFieldError('email', 'Please enter a valid email address');
      isValid = false;
    } else {
      showFieldSuccess('email');
    }
    
    // Validate subject
    const subject = formData.get('subject');
    if (!subject) {
      showFieldError('subject', 'Please select a subject');
      isValid = false;
    } else {
      showFieldSuccess('subject');
    }
    
    // Validate message
    const message = formData.get('message').trim();
    if (message.length < 10) {
      showFieldError('message', 'Message must be at least 10 characters long');
      isValid = false;
    } else if (message.length > 500) {
      showFieldError('message', 'Message must be less than 500 characters');
      isValid = false;
    } else {
      showFieldSuccess('message');
    }
    
    return isValid;
  }
  
  function showFieldError(fieldName, message) {
    const field = document.getElementById(fieldName);
    const formGroup = field.closest('.form-group');
    const errorElement = document.getElementById(fieldName + 'Error');
    
    formGroup.classList.add('error');
    errorElement.textContent = message;
    errorElement.classList.add('show');
  }
  
  function showFieldSuccess(fieldName) {
    const field = document.getElementById(fieldName);
    const formGroup = field.closest('.form-group');
    formGroup.classList.add('success');
  }
  
  // Show loading state
  function showLoading() {
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
    formMessages.style.display = 'none';
  }
  
  // Hide loading state
  function hideLoading() {
    submitBtn.classList.remove('loading');
    submitBtn.disabled = false;
  }
  
  // Show success message
  function showSuccess() {
    formMessages.style.display = 'block';
    successMessage.style.display = 'block';
    errorMessage.style.display = 'none';
    
    // Scroll to success message
    formMessages.scrollIntoView({ behavior: 'smooth' });
    
    // Add haptic feedback if supported
    if (navigator.vibrate) {
      navigator.vibrate([100, 50, 100]);
    }
  }
  
  // Show error message
  function showError(errorText = null) {
    formMessages.style.display = 'block';
    errorMessage.style.display = 'block';
    successMessage.style.display = 'none';
    
    if (errorText) {
      const errorParagraph = errorMessage.querySelector('p');
      errorParagraph.innerHTML = errorText;
    }
    
    // Scroll to error message
    formMessages.scrollIntoView({ behavior: 'smooth' });
    
    // Add haptic feedback if supported
    if (navigator.vibrate) {
      navigator.vibrate([200, 100, 200]);
    }
  }
  
  // Reset form
  function resetForm() {
    contactForm.reset();
    charCount.textContent = '0';
    charCount.style.color = '#6b7280';
    
    // Clear validation states
    document.querySelectorAll('.form-group').forEach(group => {
      group.classList.remove('error', 'success');
    });
    document.querySelectorAll('.form-error').forEach(error => {
      error.classList.remove('show');
    });
  }
  
  // Handle form submission
  contactForm.addEventListener('submit', async function(e) {
    e.preventDefault();

    // Validate form
    if (!validateForm()) {
      return;
    }

    // Show loading state
    showLoading();

    // Demo mode - simulate email sending
    if (DEMO_MODE) {
      console.log('Demo Mode: Form submission simulated');
      const formData = new FormData(contactForm);
      console.log('Form Data:', {
        name: formData.get('from_name'),
        email: formData.get('from_email'),
        subject: formData.get('subject'),
        message: formData.get('message')
      });

      // Simulate processing time
      setTimeout(() => {
        hideLoading();
        showSuccess();

        // Show demo notice
        const successMsg = document.querySelector('#successMessage p');
        successMsg.innerHTML = `
          <strong>Demo Mode Active:</strong> Your message has been validated successfully!
          <br><br>
          To enable real email sending:
          <br>1. Follow the setup guide in EMAILJS_SETUP_GUIDE.md
          <br>2. Configure your EmailJS credentials in contact.js
          <br>3. Set DEMO_MODE = false
          <br><br>
          Your message would be sent to: <strong><EMAIL></strong>
        `;

        // Reset form after demo
        setTimeout(() => {
          resetForm();
        }, 5000);
      }, 2000);

      return;
    }

    try {
      // Prepare template parameters
      const formData = new FormData(contactForm);
      const templateParams = {
        from_name: formData.get('from_name'),
        from_email: formData.get('from_email'),
        subject: formData.get('subject'),
        message: formData.get('message'),
        to_email: '<EMAIL>'
      };

      // Send email using EmailJS
      const response = await emailjs.send(
        EMAILJS_CONFIG.serviceID,
        EMAILJS_CONFIG.templateID,
        templateParams
      );

      console.log('Email sent successfully:', response);

      // Show success message
      hideLoading();
      showSuccess();

      // Reset form after successful submission
      setTimeout(() => {
        resetForm();
      }, 2000);

    } catch (error) {
      console.error('Error sending email:', error);

      hideLoading();

      // Show appropriate error message based on error type
      if (error.status === 400) {
        showError('Please check your form data and try again.');
      } else if (error.status === 401) {
        showError('Authentication error. Please contact us directly at <a href="mailto:<EMAIL>"><EMAIL></a>');
      } else {
        showError('Network error. Please check your internet connection and try again.');
      }
    }
  });
  
  // Real-time validation
  const formFields = ['name', 'email', 'subject', 'message'];
  formFields.forEach(fieldName => {
    const field = document.getElementById(fieldName);
    field.addEventListener('blur', function() {
      // Only validate if field has content
      if (this.value.trim()) {
        validateForm();
      }
    });
  });
});
