document.addEventListener('DOMContentLoaded', () => {
  // DOM Elements
  const metricBtn = document.getElementById('metricBtn');
  const imperialBtn = document.getElementById('imperialBtn');
  const metricInputs = document.querySelector('.metric-inputs');
  const imperialInputs = document.querySelector('.imperial-inputs');
  const calculateBtn = document.getElementById('calculate');
  const resultsSection = document.getElementById('results');
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');
  const saveResultsBtn = document.getElementById('saveResults');
  const shareResultsBtn = document.getElementById('shareResults');
  
  // Toggle between metric and imperial units
  metricBtn.addEventListener('click', () => {
    metricBtn.classList.add('active');
    imperialBtn.classList.remove('active');
    metricInputs.style.display = 'grid';
    imperialInputs.style.display = 'none';
    
    // Copy values from imperial to metric
    if (document.getElementById('ageImperial').value) {
      document.getElementById('age').value = document.getElementById('ageImperial').value;
    }
    if (document.getElementById('genderImperial').value) {
      document.getElementById('gender').value = document.getElementById('genderImperial').value;
    }
    if (document.getElementById('weightLbs').value) {
      const weightKg = parseFloat(document.getElementById('weightLbs').value) * 0.453592;
      document.getElementById('weight').value = weightKg.toFixed(1);
    }
    if (document.getElementById('heightFt').value && document.getElementById('heightIn').value) {
      const heightCm = (parseFloat(document.getElementById('heightFt').value) * 30.48) + 
                       (parseFloat(document.getElementById('heightIn').value) * 2.54);
      document.getElementById('height').value = heightCm.toFixed(0);
    }
    if (document.getElementById('activityImperial').value) {
      document.getElementById('activity').value = document.getElementById('activityImperial').value;
    }
  });
  
  imperialBtn.addEventListener('click', () => {
    imperialBtn.classList.add('active');
    metricBtn.classList.remove('active');
    imperialInputs.style.display = 'grid';
    metricInputs.style.display = 'none';
    
    // Copy values from metric to imperial
    if (document.getElementById('age').value) {
      document.getElementById('ageImperial').value = document.getElementById('age').value;
    }
    if (document.getElementById('gender').value) {
      document.getElementById('genderImperial').value = document.getElementById('gender').value;
    }
    if (document.getElementById('weight').value) {
      const weightLbs = parseFloat(document.getElementById('weight').value) * 2.20462;
      document.getElementById('weightLbs').value = weightLbs.toFixed(0);
    }
    if (document.getElementById('height').value) {
      const totalInches = parseFloat(document.getElementById('height').value) / 2.54;
      const feet = Math.floor(totalInches / 12);
      const inches = Math.round(totalInches % 12);
      document.getElementById('heightFt').value = feet;
      document.getElementById('heightIn').value = inches;
    }
    if (document.getElementById('activity').value) {
      document.getElementById('activityImperial').value = document.getElementById('activity').value;
    }
  });
  
  // Tab functionality
  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => btn.classList.remove('active'));
      tabContents.forEach(content => content.classList.remove('active'));
      
      // Add active class to clicked button and corresponding content
      button.classList.add('active');
      const tabId = `${button.dataset.tab}-tab`;
      document.getElementById(tabId).classList.add('active');
    });
  });
  
  // Calculate metrics when button is clicked
  calculateBtn.addEventListener('click', () => {
    let weight, height, age, gender, activityLevel;
    
    if (metricBtn.classList.contains('active')) {
      // Metric calculation
      weight = parseFloat(document.getElementById('weight').value);
      height = parseFloat(document.getElementById('height').value) / 100; // cm to m
      age = parseInt(document.getElementById('age').value);
      gender = document.getElementById('gender').value;
      activityLevel = document.getElementById('activity').value;
      
      if (!weight || !height || !age) {
        alert('Please enter all required values');
        return;
      }
    } else {
      // Imperial calculation
      const weightLbs = parseFloat(document.getElementById('weightLbs').value);
      const heightFt = parseFloat(document.getElementById('heightFt').value);
      const heightIn = parseFloat(document.getElementById('heightIn').value) || 0;
      age = parseInt(document.getElementById('ageImperial').value);
      gender = document.getElementById('genderImperial').value;
      activityLevel = document.getElementById('activityImperial').value;
      
      if (!weightLbs || !heightFt || !age) {
        alert('Please enter all required values');
        return;
      }
      
      // Convert to metric
      weight = weightLbs * 0.453592;
      height = ((heightFt * 12) + heightIn) * 0.0254;
    }
    
    // Calculate metrics
    const bmi = calculateBMI(weight, height);
    const bmr = calculateBMR(weight, height * 100, age, gender);
    const tdee = calculateTDEE(bmr, activityLevel);
    const idealWeight = calculateIdealWeight(height, gender);
    
    // Display results
    displayResults(bmi, bmr, tdee, idealWeight, gender, activityLevel);
  });
  
  // Save results functionality
  saveResultsBtn.addEventListener('click', () => {
    alert('Results saved successfully!');
    // In a real app, this would save to localStorage or a database
  });
  
  // Share results functionality
  shareResultsBtn.addEventListener('click', () => {
    if (navigator.share) {
      navigator.share({
        title: 'My Fitness Results',
        text: `My BMI is ${document.getElementById('bmiValue').textContent} (${document.getElementById('bmiCategory').textContent})`
      })
      .catch(error => console.log('Error sharing:', error));
    } else {
      alert('Web Share API not supported in your browser');
    }
  });
  
  // Calculation functions
  function calculateBMI(weight, height) {
    return weight / (height * height);
  }
  
  function calculateBMR(weight, heightCm, age, gender) {
    if (gender === 'male') {
      return 10 * weight + 6.25 * heightCm - 5 * age + 5;
    } else {
      return 10 * weight + 6.25 * heightCm - 5 * age - 161;
    }
  }
  
  function calculateTDEE(bmr, activityLevel) {
    const activityMultipliers = {
      sedentary: 1.2,
      light: 1.375,
      moderate: 1.55,
      active: 1.725,
      very: 1.9
    };
    
    return Math.round(bmr * activityMultipliers[activityLevel]);
  }
  
  function calculateIdealWeight(height, gender) {
    // Hamwi formula
    const heightCm = height * 100;
    const heightInches = heightCm / 2.54;
    
    let baseWeight;
    if (gender === 'male') {
      baseWeight = 48 + 2.7 * (heightInches - 60);
    } else {
      baseWeight = 45.5 + 2.2 * (heightInches - 60);
    }
    
    return Math.round(baseWeight);
  }
  
  function getBMICategory(bmi) {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal weight';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }
  
  function getGeneralRecommendation(bmiCategory) {
    switch (bmiCategory) {
      case 'Underweight':
        return 'You may need to gain some weight. Focus on nutrient-dense foods and strength training to build muscle mass. Consider consulting with a healthcare provider to rule out any underlying conditions.';
      case 'Normal weight':
        return 'Great job! You\'re at a healthy weight. Focus on maintaining your current habits with regular exercise and a balanced diet. Aim for 150 minutes of moderate activity per week and strength training twice weekly.';
      case 'Overweight':
        return 'Consider losing some weight through a combination of diet and exercise. Aim for a gradual weight loss of 0.5-1 kg per week by creating a moderate calorie deficit and increasing physical activity.';
      case 'Obese':
        return 'It\'s recommended to start a weight loss program under medical supervision. Focus on sustainable lifestyle changes rather than quick fixes. Even a modest weight loss of 5-10% can significantly improve your health.';
    }
  }
  
  function getNutritionRecommendation(bmiCategory, tdee, gender) {
    let calorieAdjustment, proteinPercent, carbsPercent, fatPercent;
    
    switch (bmiCategory) {
      case 'Underweight':
        calorieAdjustment = 0.2;
        proteinPercent = 20;
        carbsPercent = 50;
        fatPercent = 30;
        break;
      case 'Normal weight':
        calorieAdjustment = 0;
        proteinPercent = 15;
        carbsPercent = 50;
        fatPercent = 35;
        break;
      case 'Overweight':
        calorieAdjustment = -0.1;
        proteinPercent = 15;
        carbsPercent = 45;
        fatPercent = 40;
        break;
      case 'Obese':
        calorieAdjustment = -0.2;
        proteinPercent = 15;
        carbsPercent = 40;
        fatPercent = 45;
        break;
    }
    
    const adjustedTDEE = Math.round(tdee * (1 + calorieAdjustment));
    
    return {
      adjustedTDEE,
      protein: `${proteinPercent}% of calories`,
      carbs: `${carbsPercent}% of calories`,
      fat: `${fatPercent}% of calories`
    };
  }
  
  function getExerciseRecommendation(bmiCategory, activityLevel, gender) {
    let recommendation = '';
    let workoutPlan = '';
    
    // Base recommendations by BMI category
    switch (bmiCategory) {
      case 'Underweight':
        recommendation = 'Focus on strength training to build muscle mass. Start with bodyweight exercises and gradually add resistance. Include protein-rich foods in your diet to support muscle growth.';
        break;
      case 'Normal weight':
        recommendation = 'Maintain your fitness with a balanced approach of cardio, strength training, and flexibility work. Aim for 150 minutes of moderate activity per week plus strength training 2-3 times weekly.';
        break;
      case 'Overweight':
        recommendation = 'Combine cardio and strength training for optimal fat loss while preserving muscle. Start with low-impact activities if you\'re new to exercise, and gradually increase intensity.';
        break;
      case 'Obese':
        recommendation = 'Begin with gentle, low-impact exercises like walking, swimming, or water aerobics. Focus on consistency rather than intensity, and gradually increase duration before increasing intensity.';
        break;
    }
    
    // Add activity level specific advice
    switch (activityLevel) {
      case 'sedentary':
        recommendation += ' Since you\'re currently sedentary, start with just 10-15 minutes of activity daily and gradually build up. Walking, gentle stretching, and basic bodyweight exercises are great starting points.';
        break;
      case 'light':
        recommendation += ' With your light activity level, try to increase to 30 minutes of moderate exercise 5 days a week. Add 1-2 strength training sessions to your routine.';
        break;
      case 'moderate':
        recommendation += ' You\'re already moderately active, which is great! Consider adding variety to your routine with interval training, new sports, or group fitness classes to keep motivated.';
        break;
      case 'active':
        recommendation += ' As an active individual, focus on optimizing recovery between workouts with proper nutrition, sleep, and active recovery techniques like yoga or swimming.';
        break;
      case 'very':
        recommendation += ' At your very active level, prioritize recovery and injury prevention. Ensure you\'re balancing high-intensity work with adequate rest, proper nutrition, and mobility exercises.';
        break;
    }
    
    // Generate sample workout plan based on BMI and activity level
    let workoutLevel = 'beginner';
    if (activityLevel === 'moderate') workoutLevel = 'intermediate';
    if (activityLevel === 'active' || activityLevel === 'very') workoutLevel = 'advanced';
    
    let workoutGoal = 'strength';
    if (bmiCategory === 'Overweight' || bmiCategory === 'Obese') workoutGoal = 'weight-loss';
    if (bmiCategory === 'Normal weight' && (activityLevel === 'active' || activityLevel === 'very')) workoutGoal = 'cardio';
    
    // Create workout plan HTML
    workoutPlan = `
      <h4>Recommended Workout Plan</h4>
      <p>Based on your metrics, we recommend starting with our <strong>${workoutLevel} ${workoutGoal}</strong> program:</p>
      <div class="recommended-workout">
        <a href="#workout" class="btn-secondary" onclick="highlightWorkout('${workoutLevel}', '${workoutGoal}')">
          View Recommended Workout
        </a>
      </div>
    `;
    
    return {
      recommendation,
      workoutPlan
    };
  }

  function highlightWorkout(level, goal) {
    // First activate the correct filter buttons
    document.querySelectorAll('.filter-group:nth-child(1) .filter-btn').forEach(btn => {
      if (btn.dataset.filter === level) {
        btn.click();
      }
    });
    
    document.querySelectorAll('.filter-group:nth-child(2) .filter-btn').forEach(btn => {
      if (btn.dataset.filter === goal) {
        btn.click();
      }
    });
    
    // Scroll to workout section
    document.getElementById('workout').scrollIntoView({ behavior: 'smooth' });
    
    // Highlight the recommended workout card
    setTimeout(() => {
      const workoutCard = document.querySelector(`.workout-card[data-level="${level}"][data-goal="${goal}"]`);
      if (workoutCard) {
        workoutCard.style.boxShadow = '0 0 0 3px var(--primary-color)';
        setTimeout(() => {
          workoutCard.style.boxShadow = '';
        }, 3000);
      }
    }, 1000);
  }

  function displayResults(bmi, bmr, tdee, idealWeight, gender, activityLevel) {
    const bmiValue = document.getElementById('bmiValue');
    const bmiCategory = document.getElementById('bmiCategory');
    const bmrValue = document.getElementById('bmrValue');
    const tdeeValue = document.getElementById('tdeeValue');
    const idealWeightValue = document.getElementById('idealWeight');
    const generalRecommendation = document.getElementById('generalRecommendation');
    const nutritionRecommendation = document.getElementById('nutritionRecommendation');
    const exerciseRecommendation = document.getElementById('exerciseRecommendation');
    const workoutPlan = document.getElementById('workoutPlan');
    
    const category = getBMICategory(bmi);
    
    bmiValue.textContent = bmi.toFixed(1);
    bmiCategory.textContent = category;
    bmrValue.textContent = Math.round(bmr);
    tdeeValue.textContent = Math.round(tdee);
    idealWeightValue.textContent = `${idealWeight} kg`;
    
    generalRecommendation.textContent = getGeneralRecommendation(category);
    
    const nutritionRec = getNutritionRecommendation(category, tdee, gender);
    
    nutritionRecommendation.innerHTML = `
      <p>Based on your BMI and activity level, your daily calorie needs are approximately <strong>${nutritionRec.adjustedTDEE} calories</strong>.</p>
      <p>For optimal health, aim for:</p>
      <ul>
        <li><strong>${nutritionRec.protein}</strong> for protein</li>
        <li><strong>${nutritionRec.carbs}</strong> for carbohydrates</li>
        <li><strong>${nutritionRec.fat}</strong> for healthy fats</li>
      </ul>
    `;
    
    // Update macros chart
    document.getElementById('proteinPercent').textContent = nutritionRec.protein;
    document.getElementById('carbsPercent').textContent = nutritionRec.carbs;
    document.getElementById('fatPercent').textContent = nutritionRec.fat;
    
    // Add exercise recommendations
    const exerciseRec = getExerciseRecommendation(category, activityLevel, gender);
    exerciseRecommendation.textContent = exerciseRec.recommendation;
    workoutPlan.innerHTML = exerciseRec.workoutPlan;
    
    resultsSection.style.display = 'block';
    
    // Scroll to results
    resultsSection.scrollIntoView({ behavior: 'smooth' });
  }
});

// Workout filter functionality
document.addEventListener('DOMContentLoaded', () => {
  const filterButtons = document.querySelectorAll('.filter-btn');
  const workoutCards = document.querySelectorAll('.workout-card');
  
  // Initialize filters
  updateWorkoutDisplay();
  
  filterButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Toggle active class within the same filter group
      const filterGroup = button.closest('.filter-group');
      filterGroup.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      button.classList.add('active');
      
      // Update displayed workouts
      updateWorkoutDisplay();
    });
  });
  
  function updateWorkoutDisplay() {
    // Get active filters
    const activeLevel = document.querySelector('.filter-group:nth-child(1) .filter-btn.active').dataset.filter;
    const activeGoal = document.querySelector('.filter-group:nth-child(2) .filter-btn.active').dataset.filter;
    
    // Show/hide workout cards based on filters
    workoutCards.forEach(card => {
      if (card.dataset.level === activeLevel && card.dataset.goal === activeGoal) {
        card.style.display = 'block';
      } else {
        card.style.display = 'none';
      }
    });
  }
});

// Function to filter workouts by goal
function filterWorkoutsByGoal(goal) {
  // Scroll to workout section
  document.getElementById('workout').scrollIntoView({ behavior: 'smooth' });
  
  // Find and click the corresponding goal filter button
  setTimeout(() => {
    document.querySelectorAll('.filter-group:nth-child(2) .filter-btn').forEach(btn => {
      if (btn.dataset.filter === goal) {
        btn.click();
      }
    });
  }, 500);
}

// Theme toggle functionality
document.addEventListener('DOMContentLoaded', () => {
  const themeToggle = document.getElementById('themeToggle');
  const themeIcon = themeToggle.querySelector('i');
  
  // Always start with light theme
  document.documentElement.setAttribute('data-theme', 'light');
  themeIcon.classList.replace('fa-sun', 'fa-moon');
  localStorage.setItem('theme', 'light');
  
  // Toggle theme when button is clicked
  themeToggle.addEventListener('click', () => {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    let newTheme = 'light';
    
    if (currentTheme !== 'dark') {
      newTheme = 'dark';
      themeIcon.classList.replace('fa-moon', 'fa-sun');
    } else {
      newTheme = 'light';
      themeIcon.classList.replace('fa-sun', 'fa-moon');
    }
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
  });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function(e) {
    e.preventDefault();
    
    const targetId = this.getAttribute('href');
    const targetElement = document.querySelector(targetId);
    
    if (targetElement) {
      window.scrollTo({
        top: targetElement.offsetTop - 80, // Offset for header
        behavior: 'smooth'
      });
      
      // Update active nav link
      document.querySelectorAll('nav a').forEach(navLink => {
        navLink.classList.remove('active');
      });
      this.classList.add('active');
    }
  });
});

// Enhanced Animation System - Animate elements when they come into view
document.addEventListener('DOMContentLoaded', () => {
  // Enhanced observer options for better performance and smoother animations
  const observerOptions = {
    root: null,
    rootMargin: '50px 0px -50px 0px', // Start animation slightly before element is fully visible
    threshold: [0.1, 0.3] // Multiple thresholds for more precise control
  };

  // Create intersection observer with enhanced logic
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio >= 0.1) {
        // Add a small delay to ensure smooth animation
        setTimeout(() => {
          entry.target.classList.add('animate');
        }, 50);

        // Unobserve after animation starts to improve performance
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Initialize animation system
  function initializeAnimations() {
    // Select all elements that should be animated
    const animateElements = document.querySelectorAll('.card, .workout-card, .step-card, .nutrition-card, .goal-card');

    animateElements.forEach((el, index) => {
      // Ensure elements start in hidden state
      if (!el.classList.contains('animate')) {
        // Add a slight delay based on element type and position for natural staggering
        const elementType = el.className.split(' ')[0];
        let baseDelay = 0;

        switch(elementType) {
          case 'workout-card':
            baseDelay = index * 100; // 100ms between workout cards
            break;
          case 'step-card':
            baseDelay = index * 150; // 150ms between step cards
            break;
          case 'nutrition-card':
            baseDelay = index * 120; // 120ms between nutrition cards
            break;
          default:
            baseDelay = index * 100;
        }

        // Store the delay for later use
        el.dataset.animationDelay = baseDelay;
      }

      // Start observing the element
      observer.observe(el);
    });
  }

  // Initialize animations on page load
  initializeAnimations();

  // Re-initialize animations when workout filters change (for dynamic content)
  const filterButtons = document.querySelectorAll('.filter-btn');
  filterButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Small delay to allow filter animation to complete
      setTimeout(() => {
        // Re-observe newly visible workout cards
        const visibleWorkoutCards = document.querySelectorAll('.workout-card[style*="block"], .workout-card:not([style*="none"])');
        visibleWorkoutCards.forEach(card => {
          if (!card.classList.contains('animate')) {
            // Reset animation state for newly visible cards
            card.classList.remove('animate');
            observer.observe(card);
          }
        });
      }, 100);
    });
  });

  // Add smooth scroll behavior enhancement
  function enhanceScrollBehavior() {
    // Add momentum scrolling for better mobile experience
    document.body.style.webkitOverflowScrolling = 'touch';

    // Optimize scroll performance
    let ticking = false;

    function updateAnimations() {
      // This function can be used for scroll-based animations if needed
      ticking = false;
    }

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(updateAnimations);
        ticking = true;
      }
    }

    // Throttled scroll listener for performance
    window.addEventListener('scroll', requestTick, { passive: true });
  }

  enhanceScrollBehavior();
});


