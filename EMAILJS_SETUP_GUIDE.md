# EmailJS Setup Guide for Contact Form

This guide will help you set up EmailJS to receive contact form submissions directly to your email address: **<EMAIL>**

## Step 1: Create EmailJS Account

1. Go to [https://www.emailjs.com/](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

## Step 2: Create Email Service

1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose your email provider (Gmail is recommended)
4. For Gmail:
   - Service ID: `service_gmail` (or choose your own)
   - Click "Connect Account" and authorize with your Gmail account (<EMAIL>)
5. Save the service

## Step 3: Create Email Template

1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Template ID: `template_contact_form` (or choose your own)
4. Use this template content:

```
Subject: New Contact Form Submission - {{subject}}

From: {{from_name}} <{{from_email}}>
Subject: {{subject}}

Message:
{{message}}

---
This message was sent from the FitnessPro AI contact form.
Reply directly to this email to respond to the sender.
```

5. Save the template

## Step 4: Get Your Credentials

1. Go to "Account" → "General"
2. Copy your **Public Key** (starts with something like "user_...")
3. Note your **Service ID** from Step 2
4. Note your **Template ID** from Step 3

## Step 5: Update the JavaScript Configuration

Open `contact.js` and replace the placeholder values:

```javascript
const EMAILJS_CONFIG = {
  serviceID: 'service_gmail', // Your service ID from Step 2
  templateID: 'template_contact_form', // Your template ID from Step 3
  publicKey: 'your_actual_public_key_here' // Your public key from Step 4
};
```

## Step 6: Test the Contact Form

1. Open your website
2. Navigate to the contact page
3. Fill out the form with test data
4. Submit the form
5. Check your email (<EMAIL>) for the test message

## Email Template Variables

The following variables are automatically populated in your email template:

- `{{from_name}}` - The sender's name
- `{{from_email}}` - The sender's email address
- `{{subject}}` - The selected subject category
- `{{message}}` - The message content
- `{{to_email}}` - Your email address (<EMAIL>)

## Troubleshooting

### Common Issues:

1. **401 Unauthorized Error**
   - Check that your public key is correct
   - Ensure your EmailJS account is verified

2. **400 Bad Request Error**
   - Verify your service ID and template ID are correct
   - Check that all required template variables are included

3. **Network Errors**
   - Check internet connection
   - Verify EmailJS service is not down

4. **Emails Not Received**
   - Check spam/junk folder
   - Verify the email service is connected to the correct Gmail account
   - Test with a different email template

### Testing Tips:

- Use the EmailJS dashboard to send test emails
- Check the "Logs" section in EmailJS dashboard for debugging
- Enable browser console to see detailed error messages

## Security Notes

- The public key is safe to use in frontend code
- Never expose your private key in client-side code
- EmailJS automatically prevents spam and abuse
- Consider adding a rate limit for production use

## Free Plan Limits

EmailJS free plan includes:
- 200 emails per month
- 2 email services
- 2 email templates
- Basic support

For higher volume, consider upgrading to a paid plan.

## Support

If you encounter issues:
1. Check the EmailJS documentation: https://www.emailjs.com/docs/
2. Contact EmailJS support through their dashboard
3. Review the browser console for error messages

---

Once configured, your contact form will automatically send all submissions to **<EMAIL>** with professional formatting and all the user's details.
